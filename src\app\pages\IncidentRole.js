import React, { useState, useEffect } from "react";
import CreatableSelect from 'react-select/creatable';
import ListBox from "../form-elements/ListBox";
import { INCIDENT_ROLE_URL, INCIDENT_ROLE_WITH_ID_URL } from "../constants";

import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import LocationList from "../form-elements/LocationList";
import { deletePopup, singlePopup } from "../notifications/Swal";
import { Alert } from "react-bootstrap";
import SubmitCard from "../dashboard/SubmitCard";
import AddItem from "../form-elements/AddItem";
import API from "../services/API";

const IncidentRole = () => {


  const [tier1, setTier1] = useState([]);
  const [selectedTier1, setSelectedTier1] = useState({ id: '' });


  useEffect(() => { getWorkActivity() }, [])

  const getWorkActivity = async () => {
    const response = await API.get(INCIDENT_ROLE_URL);
    if (response.status === 200) {
      const tier1 = response.data;
      setTier1(tier1);
    }
  }


  const handleTier1Select = (id) => {
    setSelectedTier1(tier1.find(i => i.id === id))
  }

  const createTier1 = async (value) => {
    const response = await API.post(INCIDENT_ROLE_URL, { name: value })
    if (response.status === 200) {
      const createdTier1 = response.data;
      setTier1((prev) => [...prev, createdTier1]);
      cogoToast.info('Created!', { position: 'top-right' })

    }

  }





  const handleDeleteItem = async (mode, id) => {

    deletePopup.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,

      confirmButtonText: 'Delete'
    }).then(async (result) => {
      if (result.isConfirmed) {
        //   deleteChecklist(id);

        const response = await API.delete(INCIDENT_ROLE_WITH_ID_URL(id))

        if (response.status === 204) {

          setTier1(prev => prev.filter(i => i.id !== id))
          setSelectedTier1({ id: '' });

          singlePopup.fire(
            'Deleted!',
            '',
            'success'
          );
        }

      }
    })

  }


  const handleEditItem = async (id, name) => {
    const response = await API.patch(INCIDENT_ROLE_WITH_ID_URL(id), { name: name })
    if (response.status === 204) {
      cogoToast.success('Updated!')
    }
  }

  const updateReorder = async (items) => {
    try {
      // Update items one by one using their id and order
      for (const item of items) {
        const response = await API.patch(INCIDENT_ROLE_WITH_ID_URL(item.id), {
          name: item.name,
          order: item.order
        });
        if (response.status !== 204) {
          cogoToast.error(`Failed to update item: ${item.name}`);
          return;
        }
      }
      cogoToast.success(`Reordered!`);
    } catch (error) {
      cogoToast.error(`Error during reorder operation: ${error.message}`);
    }
  }

  const handleAddTooltip = async (id, payload) => {
    const response = await API.patch(INCIDENT_ROLE_WITH_ID_URL(id), { tooltips: payload.tooltips })
    if (response.status === 204) {
      cogoToast.success('Tooltip Updated!')
    }
  }

  return (
    <>
      <div className="">
        <div className="col-lg-12 p-0">

          <h4 className="card-title mb-3">Configure Incident Role</h4>



          <>
            <div className="row">
              <div className="col-4 p-1 ps-3">
                <ListBox handleDeleteItem={handleDeleteItem} handleEditItem={handleEditItem} handleReorderItems={(items) => { setTier1(items); updateReorder(items) }} selected={'true'} title={'Incident Roles'} onHandleCreateItem={createTier1} lists={tier1} handleSelect={handleTier1Select} selectedItem={selectedTier1} mode='tier1' tooltip={true} handleAddTooltip={handleAddTooltip} />
              </div>



            </div>
          </>






        </div>
      </div>




    </>
  );

}

export default IncidentRole;