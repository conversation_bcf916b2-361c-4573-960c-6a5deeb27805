import React, { useState, useEffect } from "react";
import CreatableSelect from 'react-select/creatable';
import ListBox from "../form-elements/ListBox";
import { EDIT_AUDIT_GMS_URL, AUDIT_GMS1_URL, AUDIT_GMS2_URL, AUDIT_GMS3_URL, AUDIT_GMS1_WITH_ID_URL, AUDIT_GMS2_WITH_ID_URL, AUDIT_GMS3_WITH_ID_URL, AUDIT_GMS1_GMS2_URL, AUDIT_GMS2_GMS3_URL, HUMAN_ONE_URL, HUMAN_ONE_TWO_URL, EDIT_HUMAN_URL } from "../constants";

import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import LocationList from "../form-elements/LocationList";
import { deletePopup, singlePopup } from "../notifications/Swal";
import { Alert } from "react-bootstrap";
import SubmitCard from "../dashboard/SubmitCard";
import AddItem from "../form-elements/AddItem";
import API from "../services/API";

const Human = () => {


    const [tier1, setTier1] = useState([]);
    const [selectedTier1, setSelectedTier1] = useState({ id: '' });
    const [selectedTier2, setSelectedTier2] = useState({ id: '' });
    const [selectedTier3, setSelectedTier3] = useState({ id: '' });

    useEffect(() => { getGMS1() }, [])

    const getGMS1 = async () => {
        const response = await API.get(HUMAN_ONE_URL);
        if (response.status === 200) {
            const tier1 = response.data;
            setTier1(tier1);
        }
    }


    const handleTier1Select = (id) => {
        setSelectedTier1(tier1.find(i => i.id === id))
    }

    const handleTier2Select = (id) => {
        setSelectedTier2(tier2.find(i => i.id === id))
    }

    const createTier1 = async (value) => {
        const response = await API.post(HUMAN_ONE_URL, { name: value })
        if (response.status === 200) {
            const createdTier1 = response.data;
            setTier1((prev) => [...prev, createdTier1]);
            cogoToast.info('Created!', { position: 'top-right' })

        }

    }

    const getTier1Tier2 = async () => {
        const response = await API.get(HUMAN_ONE_TWO_URL(selectedTier1.id));
        if (response.status === 200) {
            const tier2 = response.data;
            setTier2(tier2);

        }
    }

    const [tier2, setTier2] = useState([]);


    useEffect(() => {
        if (selectedTier1.id !== '')
            getTier1Tier2();
        setSelectedTier2({ id: '' });

    }, [selectedTier1.id])



    const createTier2 = async (value) => {
        const response = await API.post(HUMAN_ONE_TWO_URL(selectedTier1.id), { name: value })
        if (response.status === 200) {
            const createdTier2 = response.data;
            setTier2((prev) => [...prev, createdTier2]);
            cogoToast.info('Created!', { position: 'top-right' })
        }
    }





    const handleDeleteItem = async (mode, id) => {

        deletePopup.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Delete'
        }).then(async (result) => {
            if (result.isConfirmed) {
                //   deleteChecklist(id);

                const response = await API.delete(EDIT_HUMAN_URL(mode, id))

                if (response.status === 204) {
                    switch (mode) {
                        case 'tier1':
                            setTier1(prev => prev.filter(i => i.id !== id))
                            setSelectedTier1({ id: '' });
                            setSelectedTier2({ id: '' });
                            break;
                        case 'tier2':
                            setTier2(prev => prev.filter(i => i.id !== id))
                            setSelectedTier2({ id: '' });




                        default: break;
                    }
                    singlePopup.fire(
                        'Deleted!',
                        '',
                        'success'
                    );
                }

            }
        })

    }

    const handleEditItem = async (id, name, mode) => {
        const response = await API.patch(EDIT_HUMAN_URL(mode, id), { name: name })
        if (response.status === 204) {
            cogoToast.success('Updated!')
        }
    }

    const updateReorder = async (mode, items) => {
        try {
            // Update items one by one using their id and order
            for (const item of items) {
                const response = await API.patch(EDIT_HUMAN_URL(mode, item.id), {
                    name: item.name,
                    order: item.order
                });
                if (response.status !== 204) {
                    cogoToast.error(`Failed to update item: ${item.name}`);
                    return;
                }
            }
            cogoToast.success(`Reordered!`);
        } catch (error) {
            cogoToast.error(`Error during reorder operation: ${error.message}`);
        }
    }



    return (
        <>
            <div className="">
                <div className="col-lg-12 p-0">

                    <h4 className="card-title mb-3">Configure Body Parts & Injuries</h4>



                    <>
                        <div className="row">
                            <div className="col-4 p-1 ps-3">
                                <ListBox handleDeleteItem={handleDeleteItem} handleEditItem={(id, name) => handleEditItem(id, name, 'tier1')} handleReorderItems={(items) => { setTier1(items); updateReorder('tier1', items) }} selected={'true'} title={'Body Parts'} onHandleCreateItem={createTier1} lists={tier1} handleSelect={handleTier1Select} selectedItem={selectedTier1} mode='tier1' />
                            </div>
                            <div className="col-4 p-1">
                                <ListBox handleDeleteItem={handleDeleteItem} handleEditItem={(id, name) => handleEditItem(id, name, 'tier2')} handleReorderItems={(items) => { setTier2(items); updateReorder('tier2', items) }} title={'Injuries'} onHandleCreateItem={createTier2} lists={tier2} selected={selectedTier1.id !== ''} handleSelect={handleTier2Select} selectedItem={selectedTier2} mode='tier2' />
                            </div>


                        </div>
                    </>






                </div>
            </div>




        </>
    );

}

export default Human;