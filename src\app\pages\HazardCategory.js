import React, { useState, useEffect } from "react";

import ListBox from "../form-elements/ListBox";
import { HAZARD_CATEGORY_URL, HAZARD_CATEGORY_TYPE_URL, HAZARD_TYPE_DESCRIPTION_URL, HAZARD_DESCRIPTION_GMS_URL, EDIT_HAZARD_URL } from "../constants";


import cogoToast from 'cogo-toast';

import { deletePopup, singlePopup } from "../notifications/Swal";

import API from "../services/API";

const HazardCategory = () => {


    const [tier1, setTier1] = useState([]);
    const [selectedTier1, setSelectedTier1] = useState({ id: '' });
    const [selectedTier2, setSelectedTier2] = useState({ id: '' });
    const [selectedTier3, setSelectedTier3] = useState({ id: '' });
    const [selectedTier4, setSelectedTier4] = useState({ id: '' });

    useEffect(() => { getGMS1() }, [])

    const getGMS1 = async () => {
        const response = await API.get(HAZARD_CATEGORY_URL);
        if (response.status === 200) {
            const tier1 = response.data;
            setTier1(tier1);
        }
    }


    const handleTier1Select = (id) => {
        setSelectedTier1(tier1.find(i => i.id === id))
    }

    const handleTier2Select = (id) => {
        setSelectedTier2(tier2.find(i => i.id === id))
    }

    const handleTier3Select = (id) => {
        setSelectedTier3(tier3.find(i => i.id === id))
    }

    const createTier1 = async (value) => {
        const response = await API.post(HAZARD_CATEGORY_URL, { name: value })
        if (response.status === 200) {
            const createdTier1 = response.data;
            setTier1((prev) => [...prev, createdTier1]);
            cogoToast.info('Created!', { position: 'top-right' })

        }

    }

    const getTier1Tier2 = async () => {
        const response = await API.get(HAZARD_CATEGORY_TYPE_URL(selectedTier1.id));
        if (response.status === 200) {
            const tier2 = response.data;
            setTier2(tier2);

        }
    }

    const [tier2, setTier2] = useState([]);


    useEffect(() => {
        if (selectedTier1.id !== '')
            getTier1Tier2();
        setSelectedTier2({ id: '' });

    }, [selectedTier1.id])



    const createTier2 = async (value) => {
        const response = await API.post(HAZARD_CATEGORY_TYPE_URL(selectedTier1.id), { name: value })
        if (response.status === 200) {
            const createdTier2 = response.data;
            setTier2((prev) => [...prev, createdTier2]);
            cogoToast.info('Created!', { position: 'top-right' })
        }
    }



    const [tier3, setTier3] = useState([]);


    useEffect(() => {
        if (selectedTier2.id !== '')
            getTier2Tier3();
        setSelectedTier3({ id: '' });

    }, [selectedTier2.id])

    const getTier2Tier3 = async () => {
        const response = await API.get(HAZARD_TYPE_DESCRIPTION_URL(selectedTier2.id));

        if (response.status === 200) {
            const tier3 = response.data;
            setTier3(tier3);
        }
    }

    const createTier3 = async (value) => {
        const response = await API.post(HAZARD_TYPE_DESCRIPTION_URL(selectedTier2.id), { name: value })
        if (response.status === 200) {
            const createdTier3 = response.data;
            setTier3((prev) => [...prev, createdTier3]);
            cogoToast.info('Created!', { position: 'top-right' })
        }
    }


    const [tier4, setTier4] = useState([]);


    useEffect(() => {
        if (selectedTier3.id !== '')
            getTier3Tier4();


    }, [selectedTier3.id])

    const getTier3Tier4 = async () => {
        const response = await API.get(HAZARD_DESCRIPTION_GMS_URL(selectedTier3.id));

        if (response.status === 200) {
            const tier4 = response.data;
            setTier4(tier4);
        }
    }

    const createTier4 = async (value) => {
        const response = await API.post(HAZARD_DESCRIPTION_GMS_URL(selectedTier3.id), { name: value })
        if (response.status === 200) {
            const createdTier4 = response.data;
            setTier4((prev) => [...prev, createdTier4]);
            cogoToast.info('Created!', { position: 'top-right' })
        }
    }



    const handleDeleteItem = async (mode, id) => {

        deletePopup.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Delete'
        }).then(async (result) => {
            if (result.isConfirmed) {
                //   deleteChecklist(id);

                const response = await API.delete(EDIT_HAZARD_URL(mode, id))

                if (response.status === 204) {
                    switch (mode) {
                        case 'tier1':
                            setTier1(prev => prev.filter(i => i.id !== id))
                            setSelectedTier1({ id: '' });
                            setSelectedTier2({ id: '' });
                            setSelectedTier3({ id: '' });
                            break;
                        case 'tier2':
                            setTier2(prev => prev.filter(i => i.id !== id))
                            setSelectedTier2({ id: '' });
                            setSelectedTier3({ id: '' });

                        case 'tier3':
                            setTier3(prev => prev.filter(i => i.id !== id))
                            setSelectedTier3({ id: '' });
                            break;

                        case 'tier3':
                            setTier4(prev => prev.filter(i => i.id !== id))

                            break;


                        default: break;
                    }
                    singlePopup.fire(
                        'Deleted!',
                        '',
                        'success'
                    );
                }

            }
        })

    }

    const handleEditItem = async (id, name, mode) => {
        const response = await API.patch(EDIT_HAZARD_URL(mode, id), { name: name })
        if (response.status === 204) {
            cogoToast.success('Updated!')
        }
    }

    const updateReorder = async (mode, items) => {
        try {
            // Update items one by one using their id and order
            for (const item of items) {
                const response = await API.patch(EDIT_HAZARD_URL(mode, item.id), {
                    name: item.name,
                    order: item.order
                });
                if (response.status !== 204) {
                    cogoToast.error(`Failed to update item: ${item.name}`);
                    return;
                }
            }
            cogoToast.success(`Reordered!`);
        } catch (error) {
            cogoToast.error(`Error during reorder operation: ${error.message}`);
        }
    }


    return (
        <>
            <div className="">
                <div className="col-lg-12 p-0">

                    <h4 className="card-title mb-3">Configure Hazard Category (QR Code Only)</h4>



                    <>
                        <div className="row">
                            <div className="col-3 p-1 ps-3">
                                <ListBox handleDeleteItem={handleDeleteItem} handleEditItem={(id, name) => handleEditItem(id, name, 'tier1')} handleReorderItems={(items) => { setTier1(items); updateReorder('tier1', items) }} selected={'true'} title={'Hazard Category (QR Code Only)'} onHandleCreateItem={createTier1} lists={tier1} handleSelect={handleTier1Select} selectedItem={selectedTier1} mode='tier1' />
                            </div>
                            <div className="col-3 p-1">
                                <ListBox handleDeleteItem={handleDeleteItem} handleEditItem={(id, name) => handleEditItem(id, name, 'tier2')} handleReorderItems={(items) => { setTier2(items); updateReorder('tier2', items) }} title={'Hazard Type'} onHandleCreateItem={createTier2} lists={tier2} selected={selectedTier1.id !== ''} handleSelect={handleTier2Select} selectedItem={selectedTier2} mode='tier2' />
                            </div>
                            <div className="col-3 p-1">
                                <ListBox handleDeleteItem={handleDeleteItem} handleEditItem={(id, name) => handleEditItem(id, name, 'tier3')} handleReorderItems={(items) => { setTier3(items); updateReorder('tier3', items) }} title={'Hazard Description'} onHandleCreateItem={createTier3} lists={tier3} handleSelect={handleTier3Select} selected={selectedTier2.id !== ''} selectedItem={selectedTier3} mode='tier3' />
                            </div>
                            <div className="col-3 p-1">
                                <ListBox handleDeleteItem={handleDeleteItem} handleEditItem={(id, name) => handleEditItem(id, name, 'tier4')} handleReorderItems={(items) => { setTier4(items); updateReorder('tier4', items) }} title={'Hazard Applicable GMS'} onHandleCreateItem={createTier4} lists={tier4} selected={selectedTier3.id !== ''} selectedItem={selectedTier4} mode='tier4' />
                            </div>


                        </div>
                    </>






                </div>
            </div>




        </>
    );

}

export default HazardCategory;